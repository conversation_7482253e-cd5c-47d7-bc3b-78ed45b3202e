// 指数数据API服务

import type { 
  BaiduIndexApiResponse, 
  ParsedIndexDataPoint, 
  IndexApiParams,
  DataFetchOptions
} from './types';
import type { IndexData } from '@/types/fund';
import { 
  parseIndexData, 
  buildIndexApiUrl, 
  createApiError,
  CacheManager,
  withRetry,
  withTimeout,
  generateCacheKey,
  validateDateRange,
  formatErrorMessage
} from './utils';

/**
 * 指数API服务类
 */
export class IndexApiService {
  private cacheManager: CacheManager;
  private defaultOptions: Required<DataFetchOptions> = {
    useCache: true,
    timeout: 10000,
    retryCount: 3,
    retryDelay: 1000
  };

  constructor() {
    this.cacheManager = CacheManager.getInstance();
  }

  /**
   * 获取指数历史数据
   */
  async getIndexData(
    indexCode: string, 
    startDate?: string, 
    endDate?: string,
    options: DataFetchOptions = {}
  ): Promise<IndexData[]> {
    const opts = { ...this.defaultOptions, ...options };

    // 验证指数代码
    if (!this.validateIndexCode(indexCode)) {
      throw createApiError('INVALID_INDEX_CODE', `无效的指数代码: ${indexCode}`);
    }

    // 验证日期范围
    if (startDate && endDate && !validateDateRange(startDate, endDate)) {
      throw createApiError('INVALID_DATE_RANGE', '开始日期不能晚于结束日期');
    }

    // 检查缓存
    const cacheKey = generateCacheKey(indexCode, startDate, endDate);
    if (opts.useCache) {
      const cachedData = this.cacheManager.get<IndexData[]>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    }

    try {
      // 计算需要获取的数据条数
      const count = this.calculateDataCount(startDate, endDate);
      
      // 获取原始数据
      const rawData = await this.fetchRawIndexData(indexCode, {
        ktype: 'week',
        count,
        endTime: endDate
      }, opts);
      
      // 解析数据
      const parsedData = parseIndexData(rawData);
      
      // 转换为项目内部格式
      let indexData = this.convertToIndexData(parsedData);
      
      // 应用日期过滤
      if (startDate || endDate) {
        indexData = this.filterByDateRange(indexData, startDate, endDate);
      }

      // 缓存结果
      if (opts.useCache) {
        this.cacheManager.set(cacheKey, indexData);
      }

      return indexData;
    } catch (error) {
      const errorMessage = formatErrorMessage(error);
      throw createApiError('API_ERROR', `获取指数数据失败: ${errorMessage}`, error);
    }
  }

  /**
   * 获取指数基本信息
   */
  async getIndexInfo(indexCode: string): Promise<{ code: string; name: string } | null> {
    if (!this.validateIndexCode(indexCode)) {
      return null;
    }

    const indexNames: Record<string, string> = {
      '000001': '上证指数',
      '399001': '深证成指',
      '399006': '创业板指',
      '000300': '沪深300',
      '000905': '中证500',
      '000016': '上证50'
    };

    const name = indexNames[indexCode];
    if (name) {
      return { code: indexCode, name };
    }

    return null;
  }

  /**
   * 验证指数代码是否有效
   */
  validateIndexCode(indexCode: string): boolean {
    // 指数代码通常是6位数字
    return /^\d{6}$/.test(indexCode);
  }

  /**
   * 获取原始指数API数据
   */
  private async fetchRawIndexData(
    indexCode: string,
    params: {
      ktype?: string;
      count?: number;
      endTime?: string;
    },
    options: Required<DataFetchOptions>
  ): Promise<BaiduIndexApiResponse> {
    const url = buildIndexApiUrl(indexCode, params);

    const fetchWithRetry = () => withRetry(
      async () => {
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        
        if (data.ResultCode !== 0) {
          throw new Error(`API错误: ${data.ResultCode}`);
        }

        return data as BaiduIndexApiResponse;
      },
      { maxRetries: options.retryCount, delay: options.retryDelay }
    );

    return withTimeout(fetchWithRetry(), options.timeout);
  }

  /**
   * 转换为项目内部数据格式
   */
  private convertToIndexData(parsedData: ParsedIndexDataPoint[]): IndexData[] {
    return parsedData.map(point => ({
      date: point.time,
      value: point.close,
      pe: undefined, // 百度API不直接提供PE数据
      pb: undefined, // 百度API不直接提供PB数据
      volume: point.volume
    }));
  }

  /**
   * 按日期范围过滤数据
   */
  private filterByDateRange(
    data: IndexData[], 
    startDate?: string, 
    endDate?: string
  ): IndexData[] {
    return data.filter(item => {
      const itemDate = new Date(item.date);
      
      if (startDate && itemDate < new Date(startDate)) {
        return false;
      }
      
      if (endDate && itemDate > new Date(endDate)) {
        return false;
      }
      
      return true;
    });
  }

  /**
   * 计算需要获取的数据条数
   */
  private calculateDataCount(startDate?: string, endDate?: string): number {
    if (!startDate || !endDate) {
      return 107; // 默认约2年的周数据
    }

    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffWeeks = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 7));
    
    return Math.min(Math.max(diffWeeks + 10, 20), 500); // 最少20条，最多500条
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cacheManager.clear();
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): { size: number } {
    return {
      size: this.cacheManager.size()
    };
  }
}

// 导出单例实例
export const indexApiService = new IndexApiService();

// 便捷函数
export async function fetchIndexData(
  indexCode: string,
  startDate?: string,
  endDate?: string,
  options?: DataFetchOptions
): Promise<IndexData[]> {
  return indexApiService.getIndexData(indexCode, startDate, endDate, options);
}

export async function getIndexBasicInfo(indexCode: string) {
  return indexApiService.getIndexInfo(indexCode);
}
